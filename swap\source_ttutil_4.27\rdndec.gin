      INTEGER NTOK
      INTEGER TEOI
      INTEGER TEOL
      INTEGER TTIME
      INTEGER TINTEG
      INTEGER TFLOAT
      INTEGER TSPACE
      INTEGER TCOMMA
      INTEGER TEQUAL
      INTEGER TSEMIC
      INTEGER TMULT
      INTEGER TLPAR
      INTEGER TRPAR
      INTEGER TSCONC
      INTEGER TCMMNT
      INTEGER TIDENT
      INTEGER TSTRNG
      INTEGER TLOGIC
      INTEGER TMISS
      PARAMETER (NTOK   = 18)
      PARAMETER (TEOI   =  1)
      PARAMETER (TEOL   =  2)
      PARAMETER (TTIME  =  3)
      PARAMETER (TINTEG =  4)
      PARAMETER (TFLOAT =  5)
      PARAMETER (TSPACE =  6)
      PARAMETER (TCOMMA =  7)
      PARAMETER (TEQUAL =  8)
      PARAMETER (TSEMIC =  9)
      PARAMETER (TMULT  = 10)
      PARAMETER (TLPAR  = 11)
      PARAMETER (TRPAR  = 12)
      PARAMETER (TSCONC = 13)
      PARAMETER (TCMMNT = 14)
      PARAMETER (TIDENT = 15)
      PARAMETER (TSTRNG = 16)
      PARAMETER (TLOGIC = 17)
      PARAMETER (TMISS  = 18)
      CHARACTER TOKENN(NTOK)*6
