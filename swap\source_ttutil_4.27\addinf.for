      SUBROUTINE ADDINF (STRING,<PERSON><PERSON><PERSON><PERSON>,I,FOR<PERSON>)
      IMPLICIT NONE

*     FORMAL_PARAMETERS:
      CHARACTER*(*) STRING,FORM
      INTEGER SIGLEN,I

**    local variables
      CHARACTER*30 TMPR
      CHARACTER*30 TMPFORM
      INTEGER IL
      SAVE

      TMPFORM = '('//FORM//',A)'
      WRITE (TMPR,TMPFORM) I,'|'
      IL = INDEX (TMPR,'|')-1

      IF (SIGLEN+IL.GT.LEN (STRING)) CALL FATALERR
     &   ('ADDREF','internal error')

      STRING(SIGLEN+1:SIGLEN+IL) = TMPR(1:IL)
      SIGLEN = SIGLEN+IL

      RETURN
      END
