      SUBROUTINE DTARDP (DATEA,FSEC,DPDTTM)
      IMPLICIT NONE

*     FORMAL_PARAMETERS:
      INTEGER DATEA(6)
      REAL FSEC
      DOUBLE PRECISION DPDTTM

**    Local variables
      INTEGER ITASK
      LOGICAL ERR
      CHARACTER*80 MESSAG
      SAVE

      ITASK = 1
      CALL DTSYS (ITASK,DATEA,FSEC,DPDTTM,ERR,MESSAG)

      IF (ERR) THEN
         DPDTTM = 0.D0
         CALL FATALERR ('DTARDP',MESSAG)
      END IF

      RETURN
      END

