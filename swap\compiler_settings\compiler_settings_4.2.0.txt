=== Compiler_Settings_4.2.0.txt ================================================================

==============================================================================
此文件是以下程序的一部分：

程序      : SWAP
版本      : 4.2.0
发布日期  : 2017年6月8日
平台      : windows10; Linux (Ubuntu)

Windows 可执行文件 swap.exe 是使用以下工具创建的：

编译器:
   Intel® Parallel Studio XE 2019 Update 6 Composer Edition for Fortran Windows Integration
   for Microsoft Visual Studio* 2017, Version 19.0.0052.15

编译器命令行设置:
   /nologo /O2 /I"D:\SYS\F77\Lib\TTUTIL4_27\TTutil427\x64\Release"
   /fpscomp:general /warn:declarations /warn:unused /warn:uncalled
   /warn:interfaces /Qsave /Qinit:zero /fpe:0 /fp:source /Qfp-speculation=off
   /module:"x64\Release\\" /object:"x64\Release\\" /Fd"x64\Release\vc150.pdb"
   /libs:static /threads /c

链接器命令行设置:
   /OUT:"x64\Release\SWAP_4.exe" /INCREMENTAL:NO /NOLOGO /MANIFEST
   /MANIFESTFILE:"x64\Release\SWAP_4.exe.intermediate.manifest"
   /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /SUBSYSTEM:CONSOLE
   /IMPLIB:"D:\USR\SWAP\SWAP_V4\SWAP_current\x64\Release\SWAP_4.lib"

Swap 的目标文件与 ttutil 4.27 版本的库链接。
此 ttutil 库是使用以下设置创建的：
   /nologo /O2 /fpscomp:general /module:"x64\Release\\"
   /object:"x64\Release\\" /Fd"x64\Release\vc150.pdb" /libs:static /threads
   /c/nologo /Od /module:"Release\\" /object:"Release\\" /libs:static /threads /c

Linux 可执行文件是使用 Intel 编译器创建的。
Bash 脚本在单独的文件中提供：compile_link_420.sh

==============================================================================

=== Compiler_Settings_4.2.0.txt 文件结束 ========================================================
